<template>
  <div class="operation-log">
    <ScreenTitle>操作日志</ScreenTitle>
    <ScreenBox class="flex-1">
      <div class="log-list box-content">
        <template v-if="logList?.length">
          <div class="log-row single-log" v-for="(item, rowIndex) in logList" :key="rowIndex">
            <div class="log-item">
              <div class="log-icon" :class="getStatusClass(item.result)"></div>
              <div class="log-content">
                <div class="log-time">{{
                  dayjs(item.operationTime).format('YYYY-MM-DD HH:mm:ss')
                }}</div>
                <div class="log-action">完成一次操作</div>
                <div class="log-tag-box">
                  <div
                    :class="['log-tag', getStatusClass(it.color)]"
                    v-for="(it, index) in item.actionLabels"
                    :key="index"
                    >{{ it.desc }}</div
                  >
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 空数据 -->
        <GeegaEmpty v-else description="暂无数据" />
      </div>
    </ScreenBox>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import dayjs from 'dayjs';
import { V1LocationStudyOperationLogPagePost } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import { useErrorSound } from '/@/composables/useErrorSound';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import { watchImmediate } from '@vueuse/core';

const props = defineProps<{
  detailId?: string;
  data?: any;
}>();

// 本地维护的日志列表
const localLogList = ref<any[]>([]);

// 初始化错误提示音
const { playErrorSound } = useErrorSound();

// 操作日志
const apiData = useAsyncData(
  async () => {
    if (Object.keys(props.data).length || !props.detailId) return { records: [] };
    const result = await V1LocationStudyOperationLogPagePost({
      pageSize: 10,
      currentPage: 1,
      data: {
        detailId: props.detailId,
      },
    });
    // 初始化本地日志列表
    localLogList.value = result.records || [];
    return result;
  },
  { records: [] }
);

watchImmediate(
  () => props.detailId,
  () => apiData.load()
);

// 清空本地日志
const clearLocalLog = () => {
  localLogList.value = [];
};

const logList = computed(() => {
  if (Object.keys(props.data).length && props.data.operationLog) {
    const newLog = props.data.operationLog;
    // 检查新日志是否已存在
    if (!localLogList.value.some((item) => item.id === newLog.id)) {
      localLogList.value = [newLog, ...localLogList.value];
      // 检查新日志的 result，如果是 0 或 2，播放对应的错误提示音
      if (newLog.result === 0 || newLog.result === 2) {
        playErrorSound(newLog.actionLabels);
      }
    }
  }
  return localLogList.value;
});

const getStatusClass = (result: number) => {
  return {
    success: result === 1,
    error: result === 0,
    warning: result === 2,
  };
};

defineExpose({
  clearLocalLog,
});
</script>

<style lang="less" scoped>
@import './style/operation-log.less';
</style>
