// 操作日志列数据
export const logColumns = () => {
  return [
    {
      title: '时间',
      dataIndex: 'time',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
  ];
};

// 训练状态枚举
export enum TrainStatusEnum {
  INIT = 'init',
  ING = 'ing',
  STOP = 'stop',
}

// 点位数据接口
export interface PointData {
  objectFlag: string;
  result: number;
  sort: number;
  status: number;
}

// 实时数据接口
export interface RealTimeData {
  totalActionRate: number; // 总动作达标率
  totalQualificationRate: number; // 总作业合格率
  totalStandardNum: number; // 达标数
  totalActionNum: number; // 总数
  totalQualifiedNum: number; // 合格数
  totalQualificationNum: number; // 总数
  totalTrainFrequency: number; // 统计次数
  opNum: number;
  trainFrequency: number;
  standardNum?: number;
  nonStandardNum?: number;
  qualificationNum?: number;
  unqualifiedNum?: number;
  actionNumOk?: number;
  actionNum?: number;
  opNumOk?: number;
  [key: string]: any; // 允许其他属性
}

// 训练进度接口
export interface TrainProgress {
  trainDuration: number;
  trainFrequency: number;
  requestDuration: number;
  requestFrequency: number;
  recordId: string;
  opNum: number;
  opNumOk: number;
  actionNum: number;
  actionNumOk: number;
  [key: string]: any; // 允许其他属性
}

// WebSocket 消息数据接口
export interface WebSocketMessageData {
  action: 'push' | 'overTrain' | string;
  standardNum?: number;
  nonStandardNum?: number;
  qualificationNum?: number;
  unqualifiedNum?: number;
  totalDuration?: number;
  [key: string]: any;
}

// WebSocket 测试消息
export const testData = {
  action: 'push',
  actionRate: '50',
  detailId: '1881287304074317825',
  nonStandardNum: 2,
  operationLog: {
    actionLabels: [
      {
        actionType: 2,
        color: 2,
        desc: '未垂直作业面',
      },
    ],
    actionType: 0,
    detailId: '1881287304074317825',
    id: '1881527673032663042',
    nodeId: '1881527673032663042',
    operationTime: '2025-01-21 10:22:09.143226700',
    result: 2,
  },
  qualificationNum: 0,
  qualificationRate: '0',
  requestActionRate: '0',
  requestQualificationRate: '0',
  standardNum: 2,
  totalDuration: 0,
  unqualifiedNum: 1,
};
